* {
	-moz-box-sizing: unset;
	-webkit-box-sizing: unset;
	-o-box-sizing: unset;
	-ms-box-sizing: unset;
	box-sizing: unset;
}

body::-webkit-scrollbar {
	display: none;
}

body {
	margin: 0;
	width: 100%;
	height: 100vh;
	background-color: #02004D;
	background-size: 100% 100%;
}


.header {
	background-image: url(img/header_bg.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	height: 80px;
	text-align: center;
	line-height: 59px;
	color: white;
	font-size: 36px;
	letter-spacing: 7px;
	font-weight: 500;
	width: 100%;
}

.search {
	margin-top: 10px;
	margin-bottom: 10px;
	margin-left: 20px;
	width: calc(100% - 20px);
	height: 30px;
}

.layui-icon-search {
	font-size: 25px;
	color: white;
}

.layui-icon-download-circle {
	font-size: 22px;
	color: white;
}

.layui-icon-refresh {
	font-size: 21px;
	color: white;
}

.layui-input,
.layui-textarea,
.layui-select {
	height: 30px;
}

.model-select {
	width: 100%;
	float: left;
	height: 30px;
}

.layui-form-item {
	margin-bottom: 0px;
}

.layui-form-item .layui-inline {
	margin-bottom: 0px;
}

.layui-form-mid {
	color: white;
	padding: 0 0 !important;
	line-height: 30px;
}

.model-select .layui-input {
	background-color: #042473;
	border-color: #006DAB;
	color: white;
}

.model-select .layui-input::-webkit-input-placeholder {
	color: white;
}

.model-select .layui-input:hover {
	border-color: #0699ed !important;
}

.model-select .layui-input:focus {
	border-color: #0699ed !important;
}

.model-select .layui-form-select dl {
	border: 1px solid #006DAB;
	background-color: #042473;
	color: white;
}

.layui-form-select .layui-edge {
	border-top-color: #00D2FF;
}

.model-select .layui-form-select dl dd.layui-select-tips {
	color: #fff;
}

.model-select .layui-form-select dl dd.layui-this {
	background-color: #00d2ffb8;
	color: white;
}


.model-select .layui-form-select dl dd:hover {
	background-color: #2983cf;
	-webkit-transition: .5s all;
	transition: .5s all;
}

.top {
	width: calc(100% - 40px);
	height: 355px;
	padding: 0 20px 10px 20px;
}

.top .card {
	background-image: url(img/bg_1_2x.png);
	background-size: 100% 100%;
	background-repeat: no-repeat;
	height: 100%;
	width: 100%;
}

.top .card-header {
	height: 35px;
	line-height: 39px;
	font-size: 24px;
	background-image: url(img/title_1_2x.png);
	letter-spacing: 5px;
}

.top .card-body {
	width: calc(100% - 20px);
	padding: 0 10px 10px 10px;
}

.content {
	width: 100%;
	height: 950px;
}

.content-left {
	width: 66.666%;
	height: 100%;
	float: left;
}

.content-left-top,
.content-left-middle,
.content-left-bottom {
	width: calc(100% - 20px);
	height: calc(33.333% - 10px);
	padding: 0 0 10px 20px;
}

.card {
	height: 100%;
	width: 100%;
}

.card-header {
	position: relative;
	height: 30px;
	letter-spacing: 2px;
	line-height: 36px;
	color: #00D1FF;
	background-repeat: no-repeat;
	background-position-x: 50.67%;
	background-size: 76.5% 100%;
	text-align: center;
	width: 100%;
	font-size: 20px;
	font-weight: 600;
}

.card-body {
	height: calc(100% - 29px);
}

.content-left-middle-left,
.content-left-bottom-left {
	width: calc(50% - 5px);
	height: 100%;
	float: left;
}

.content-left-top .card,
.content-left-middle-left .card,
.content-left-bottom-left .card,
.content-left-middle-right .card,
.content-left-bottom-right .card {
	background-image: url(img/bg_1.png);
	background-size: 100% 100%;
	background-repeat: no-repeat;
	height: 100%;
	width: 100%;
}

.content-left-top .card-header,
.content-left-middle-left .card-header,
.content-left-bottom-left .card-header,
.content-left-middle-right .card-header,
.content-left-bottom-right .card-header {
	background-image: url(img/title_1.png);
}

.content-left-middle-right,
.content-left-bottom-right {
	margin-left: 10px;
	width: calc(50% - 5px);
	height: 100%;
	float: left;
}


.content-right {
	width: 33.333%;
	height: 100%;
	float: left;
}

.content-right-top,
.content-right-bottom {
	width: calc(100% - 30px);
	height: calc(33.333% - 10px);
	padding: 0 20px 10px 10px;
}

.content-right-top .card,
.content-right-bottom .card {
	background-image: url(img/bg_1.png);
	background-size: 100% 100%;
	background-repeat: no-repeat;
	height: 100%;
	width: 100%;
}

.content-right-top .card-header,
.content-right-bottom .card-header {
	background-image: url(img/title_1.png);
}

/**
 * 滚动条样式的调整
 */
.layui-anim-upbit::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 6px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}

.layui-anim-upbit::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 6px;
	background-color: skyblue;
}

.layui-anim-upbit::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: #ededed;
	border-radius: 6px;
}

.my-layer {
	background-color: #070F77 !important;
}

.my-layer .layui-layer-title {
	border-bottom: 0px !important;
	padding-left: 20px !important;
	line-height: 35px !important;
}

.my-layer .layui-layer-title span.layui-this {
	color: #000A3B !important;
	border-left: 0px !important;
	border-right: 0px !important;
	background-color: #00FFF4 !important;
}

.my-layer .layui-table-view {
	margin: 0;
}

.layui-layer-tab .layui-layer-title span {
	color: #00FFF4 !important;
	background-color: #202784 !important;
	font-size: 15px;
	font-weight: 600;
	letter-spacing: 1px;
	height: 35px !important;
	margin-top: 15px !important;
	border-radius: 4px !important;
}

.v-mult {
	margin: 50px 0px 10px 10px;
	height: 80px;
	width: 150px;
	padding: 0px 2px;
	overflow: hidden;
	font-size: 16px;
	text-align: center;
	background-color: #169BD5;
	color: white;
	cursor: pointer;
}

.v-mult-active {
	background-color: #FF9900;
}

/* path {
			stroke: #169BD5;
			fill: #169BD5;
			stroke-width: 2px;
		} */

.v-mult .empty,
.v-mult .text {
	display: inline-block;
	vertical-align: middle;
	line-height: 20px;
}

.v-mult .empty {
	height: 100%;
}

#diagramContainer .layui-col-md2,
#diagramContainer .layui-col-md3 {
	min-height: 1px;
}


.layui-table {
	width: 100%;
	margin: 10px 0;
	background-color: #070F77;
	color: #fff;
}

.layui-table th,
.layui-table td {
	font-size: 16px;
	font-weight: 400;
}

.layui-table-view {
	border: none;
}

.layui-table-header {
	border-radius: 4px 4px 0px 0px;
	background-color: #070F77;
	border-bottom-width: 0;
}

.layui-table thead tr {
	background: linear-gradient(360deg, rgba(0, 255, 244, 0.08) 0%, rgba(0, 255, 244, 0.42) 100%);
	border: 1px solid rgba(0, 255, 244, 0.5);
}

.layui-table tr {
	background-color: #070F77;
}

.layui-table-click,
.layui-table-hover,
.layui-table[lay-even] tbody tr:nth-child(even) {
	background-color: #1A2181;
}

.layui-table-cell {
	height: 50px;
	line-height: 40px;
}

.layui-table-page {
	border-color: #070f77;
	padding: 20px 7px 0px 7px;
	height: 54px;
}

.layui-laypage .layui-laypage-skip,
.layui-laypage span {
	color: #fff;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
	background-color: #070f77;
}

.layui-laypage a {
	color: #fff !important;
}

.layui-table-view:after {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 0px !important;
	height: 100%;
	background-color: #eee;
	z-index: 101;
}

.layui-table-cell-c {
	background-color: #070f77 !important;
}

.model-process-wrapper {
	position: relative;
	overflow: hidden;
	width: 100%;
	height: 300px;
	clip-path: inset(0);
}

#modelProcess {
	overflow-y: hidden;
	overflow-x: auto;
	width: 100%;
	padding-bottom: 10px;
	height: 300px;
}

#modelProcess>table:nth-child(1) {
	/* width: 100%; */
	height: 100%;
	color: #00CDFF;
}

#modelProcess>table:nth-child(1)>tbody>tr>td {
	text-align: center;
	vertical-align: middle;
}

#modelProcess>table:nth-child(1)>tbody>tr:nth-child(1)>td {
	padding-top: 5px;
	padding-bottom: 5px;
}

#modelProcess>table:nth-child(1)>tbody>tr:nth-child(2)>td {
	vertical-align: top;
}

#modelProcess table td div {
	/* width: fit-content; */
}

#modelProcess::-webkit-scrollbar {
	/* 增加滚动条宽度，使其更明显 */
	height: 12px;
	/* 如果是横向滚动条，则用 width，如果是纵向滚动条，则用 height */
	background-color: rgba(0, 0, 0, 0.1);
	/* 给轨道添加轻微颜色以增强视觉效果 */
}

#modelProcess::-webkit-scrollbar-thumb {
	/* 增大滑块大小，使对比更明显 */
	border-radius: 10px;
	background-color: #00cdff;
	/* 使用高对比度的颜色 */
	min-width: 24px;
	/* 确保滑块高度足够，如果是横向滚动条则用 min-width */
	box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
	/* 添加内阴影以增加立体感 */
}

#modelProcess::-webkit-scrollbar-thumb:hover {
	background-color: #0095ff;
	/* 鼠标悬停时更改颜色以增强反馈 */
}

#modelProcess::-webkit-scrollbar-track {
	/* 轨道部分 */
	box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
	background: #f2f2f2;
	/* 更浅的背景色与滑块形成对比 */
	border-radius: 10px;
}

.end-point {
	cursor: pointer;
	padding-top: 7px;
	text-align: center;
	display: inline-block;
	width: 52px;
	height: 23px;
	background-image: url(img/num_bg.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
}

.end-point span {
	margin-right: 10px;
}

.start-point {
	display: flex;
	align-items: center;
	flex-direction: column;
	width: 120px;
}

.process-img {
	width: 45px;
	height: 45px;
	border-radius: 50%;
	border: 2.5px solid #02ff52;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 25px;
	color: #02ff52;
}

.process-name {
	max-width: 115px;
	height: 38px;
	margin-bottom: 5px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	font-weight: bold;
	font-size: 16px;
}

/**
 * 滚动条样式的调整
 */
.layui-anim-upbit::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 6px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}

.layui-anim-upbit::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 6px;
	background-color: skyblue;
}

.layui-anim-upbit::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: #ededed;
	border-radius: 6px;
}

.model-table-div {
	max-height: 176px;
	overflow: auto;
	width: 110px;
	margin: auto;
	padding-right: 2px;
}

.model-table-div::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}

.model-table-div::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 5px;
	background-color: #00cdff;
}

.model-table-div::-webkit-scrollbar-thumb:hover {
	/*滚动条里面小方块*/
	border-radius: 5px;
	background-color: #0095ff;
}

.model-table-div::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: #e2e2e2;
	border-radius: 5px;
}

.model-table {
	color: white;
	background-color: #00AFF0;
	margin: 0;
}

.model-table tbody tr:hover {
	background-color: #00003B;
}

.model-table td {
	border-width: 0px 0px 1px 0px;
	border-color: #00AFF0;
	font-size: 14px;
	padding: 2px 2px;
	background-color: #008cff;
}

.model-table td:hover {
	cursor: pointer;
	background-color: #00AFF0;
}

.incomplete {
	border: 2.5px solid #02ff52;
	color: #02ff52;
}

.complete {
	border: 2.5px solid gray;
	color: gray;
}

.current {
	border: 2.5px solid #00D2FF;
	color: #00D2FF;
	-webkit-animation: toRed 0.5s infinite;
	/* Chrome, Safari, Opera */
	animation: toRed 0.5s infinite;
}

/* Chrome, Safari, Opera */
@-webkit-keyframes toRed {
	50% {
		border: 2.5px solid #02ff52;
		color: #02ff52;
	}
}

/* Standard syntax */
@keyframes toRed {
	50% {
		border: 2.5px solid #02ff52;
		color: #02ff52;
	}
}